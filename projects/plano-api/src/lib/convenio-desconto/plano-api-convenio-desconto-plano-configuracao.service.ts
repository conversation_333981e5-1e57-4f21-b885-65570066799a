import { Injectable } from "@angular/core";
import { PlanoApiModule } from "../plano-api.module";
import { PlanoApiBaseService } from "../base/plano-api-base.service";
import { ApiResponseList } from "../base/base.model";
import { ConvenioDescontoPlanoConfiguracao } from "./convenio-desconto.model";
import { Plano } from "../plano/plano.model";
import { Observable } from "rxjs";

@Injectable({
	providedIn: PlanoApiModule,
})
export class PlanoApiConvenioDescontoPlanoConfiguracaoService {
	constructor(private planoApiBaseService: PlanoApiBaseService) {}

	public getConfiguracaoByConvenioDescontoPlano(
		id: number | string
	): Observable<ApiResponseList<ConvenioDescontoPlanoConfiguracao>> {
		return this.planoApiBaseService.get<
			ApiResponseList<ConvenioDescontoPlanoConfiguracao>
		>(`convenio-desconto-plano-configuracao/config/${id}`);
	}

	public getPlano(): Observable<ApiResponseList<Plano>> {
		return this.planoApiBaseService.get<ApiResponseList<Plano>>(
			`/planos/find-by-convenio-desconto-plano`
		);
	}
}
