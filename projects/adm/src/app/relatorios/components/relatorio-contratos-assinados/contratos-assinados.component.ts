import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent
} from 'ui-kit';
import { PerfilAcessoRecurso, SessionService } from "sdk";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import { AdmRestService } from '@adm/adm-rest.service';

@Component({
	selector: "adm-contratos-assinados",
	templateUrl: "./contratos-assinados.component.html",
	styleUrls: ["./contratos-assinados.component.scss"],
})
export class ContratosAssinadosComponent implements AfterViewInit {
	@ViewChild("traducoes", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnMatricula", { static: true })
	columnMatricula: TemplateRef<any>;
	@ViewChild("columnDataAssinatura", { static: true }) columnDataAssinatura: TemplateRef<any>;
	@ViewChild("celulaNome", { static: true })
	public celulaNome;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	recurso: PerfilAcessoRecurso;
	table: PactoDataGridConfig;

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService
	) {
		this.recurso = this.session.recursos.get(PerfilAcessoRecursoNome.PLANO);
	}

	ngAfterViewInit() {
		this.initTable();
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlAdmCore(`contratos/modelos-assinados/${this.session.empresaId}`),
			quickSearch: true,
			ghostLoad: true,
			ghostAmount: 5,
			columns: [
				{
					nome: "nome",
					titulo: this.columnNome,
					visible: true,
					celula: this.celulaNome,
					ordenavel: true,
				},
				{
					nome: "matricula",
					titulo: this.columnMatricula,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataAssinatura",
					titulo: this.columnDataAssinatura,
					visible: true,
					ordenavel: true,
				},
			],
			actions: [
				{
					nome: this.traducao.getLabel("action-down"),
					iconClass: "pct pct-printer cor-azulim05 pct-icon-mr-16",
					tooltipText: "Imprimir",
					actionFn: (row) => this.imprimirContrato(row.row),
				},
			],
		});
		this.cd.detectChanges();
	}

	btnClickHandler() {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	imprimirContrato(row) {
		const trueUrl = this.admRest.buildFullUrlZW(
			`faces/VisualizarContrato?k=${this.session.chave}` +
			`&c=${row.contrato}` +
			`&confirmacaoAssinatura=true` +
			`&geradoEm=${new Date().getTime()}` +
			`&geradoPor=${this.session.loggedUser.nome}`
		);
		window.open(trueUrl, "_blank");
	}
}
