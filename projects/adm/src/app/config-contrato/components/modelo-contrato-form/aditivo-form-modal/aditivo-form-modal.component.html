<div [ngClass]="{ 'loading-blur': loading }">
	<div class="aditivo-form-modal-wrapper">
		<div class="row">
			<div [ngClass]="isLog ? 'col-md-6' : 'col-md-12'">
				<pacto-cat-form-input
					[control]="formGroup.get('nome')"
					[id]="'nome-aditivo'"
					[maxlength]="100"
					errorMsg="Informe o título"
					label="Título*"
					placeholder="Título"></pacto-cat-form-input>
			</div>

			<div class="col-md-6" *ngIf="isLog">
				<pacto-cat-form-input
					[control]="formGroup.get('responsavel')"
					[id]="'responsavel-aditivo'"
					label="Usuário"></pacto-cat-form-input>
			</div>
		</div>

		<div class="row" *ngIf="isLog">
			<div class="col-md-6">
				<pacto-cat-form-datepicker
					[control]="formGroup.get('dataCriacao')"
					label="Data de criação"></pacto-cat-form-datepicker>
			</div>

			<div class="col-md-6">
				<pacto-cat-form-datepicker
					[control]="formGroup.get('dataProcessamento')"
					label="Data de processamento"></pacto-cat-form-datepicker>
			</div>
		</div>

		<div class="row" [ngStyle]="getEditorTextoStyles()">
			<div class="col-md-12 div-text-area-modelo-contrato">
				<quill-editor
					[formControl]="formGroup.get('descricao')"
					[id]="'editor-texto-modelo-contrato'"
					[modules]="modules"
					[styles]="{ height: '400px' }"></quill-editor>
			</div>
		</div>

		<div class="aditivo-form-modal-footer" *ngIf="!isLog">
			<pacto-cat-button
				[label]="'Salvar'"
				id="btn-save"
				[size]="'LARGE'"
				(click)="salvar()"></pacto-cat-button>
			<pacto-cat-button
				style="margin-right: 10px"
				[type]="'OUTLINE_ACTION'"
				[label]="'Cancelar'"
				[size]="'LARGE'"
				id="btn-cancel"
				(click)="cancelar()"></pacto-cat-button>
		</div>
	</div>
</div>
