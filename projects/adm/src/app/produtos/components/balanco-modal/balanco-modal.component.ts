import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Api } from "sdk";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { AdmRestService } from "../../../adm-rest.service";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "adm-balanco-modal",
	templateUrl: "./balanco-modal.component.html",
	styleUrls: ["./balanco-modal.component.scss"],
})
export class BalancoModalComponent implements OnInit {
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("columnProduto", { static: true }) columnProduto: TemplateRef<any>;
	@ViewChild("columnCategoria", { static: true })
	columnCategoria: TemplateRef<any>;

	table: PactoDataGridConfig;
	@Input() codigoEmpresa: number;
	// true: ira trazer todos os produtos, sem levar em conta se existe no controle de estoque ou não (usado para configurar o estoque)
	@Input() todosProdutos? = false;
	// true: trazer apenas as categorias
	@Input() onlyCategoria? = false;
	urlProduto;

	constructor(
		public dialog: NgbActiveModal,
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.initTable();
	}

	private initTable() {
		if (this.onlyCategoria === false) {
			this.urlProduto = "produtos/desc-categoria";
		} else {
			this.urlProduto = `categoria-produto/configurarProdutoEstoque/${this.onlyCategoria}`;
		}

		if (this.codigoEmpresa && this.onlyCategoria === false) {
			this.urlProduto += `/${this.codigoEmpresa}`;
			if (this.todosProdutos && this.onlyCategoria === false) {
				this.urlProduto += `/${true}`;
			}
		}

		this.table = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrl(
				this.urlProduto,
				false,
				Api.MSPRODUTO
			),
			quickSearch: true,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			dataAdapterFn: (serverData) => {
				if (
					!serverData ||
					!serverData.content ||
					serverData.content.length === 0
				) {
					this.dialog.dismiss();
					this.notificationService.warning(
						"Você não possui produtos do tipo Estoque ou Ordem de Compra cadastrados ou configurados no estoque."
					);
					return { content: [], totalElements: 0 };
				}
				return serverData;
			},
			columns: [
				// quando é categoria traz somente a descrição
				{
					nome: "descricao",
					titulo: this.columnProduto,
					visible: !this.onlyCategoria,
					ordenavel: true,
				},
				{
					nome: "descricao",
					titulo: this.columnCategoria,
					visible: this.onlyCategoria,
					ordenavel: true,
				},
				{
					nome: "categoriaProduto",
					titulo: this.columnCategoria,
					visible: !this.onlyCategoria,
					ordenavel: true,
					valueTransform: (v: any) => v.descricao,
				},
			],
		});
		this.cd.detectChanges();
	}

	selectProduto(event) {
		this.dialog.close(event);
	}
}
