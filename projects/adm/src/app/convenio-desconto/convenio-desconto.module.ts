import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { PlanoApiModule } from "plano-api";
import { UiModule } from "ui-kit";

import { ConvenioDescontoComponent } from "./components/convenio-desconto/convenio-desconto.component";
import { ConvenioDescontoFormComponent } from "./components/convenio-desconto-form/convenio-desconto-form.component";
import { TableConvenioDescontoConfigComponent } from "./components/table-convenio-desconto-config/table-convenio-desconto-config.component";
import { TableConvenioDescontoPlanoConfigComponent } from "./components/table-convenio-desconto-plano-config/table-convenio-desconto-plano-config.component";

@NgModule({
	declarations: [
		ConvenioDescontoComponent,
		ConvenioDescontoFormComponent,
		TableConvenioDescontoConfigComponent,
		TableConvenioDescontoPlanoConfigComponent,
	],
	imports: [CommonModule, ReactiveFormsModule, PlanoApiModule, UiModule],
	exports: [
		ConvenioDescontoComponent,
		ConvenioDescontoFormComponent,
		TableConvenioDescontoConfigComponent,
		TableConvenioDescontoPlanoConfigComponent,
	],
})
export class ConvenioDescontoModule {}
