import { I, <PERSON> } from "@angular/cdk/keycodes";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	Input,
	OnInit,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { CrmApiConfigIAService } from "crm-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-campanha-dialog",
	templateUrl: "./campanha-dialog.component.html",
	styleUrls: ["./campanha-dialog.component.scss"],
})
export class CampanhaDialogComponent implements OnInit {
	@Input() codigoConfiguracao: number;
	@Input() empresa: number;
	@Input() campanha: any;
	@Input() idInstancia: string;
	@Input() token: string;

	formGroup: FormGroup;
	imagemPreviewUrl: string | ArrayBuffer | null = null;
	phone: string = "";
	constructor(
		private formBuilder: FormBuilder,
		public dialogRef: MatDialogRef<CampanhaDialogComponent>,
		private notificationService: SnotifyService,
		private openModal: NgbActiveModal,
		private apiConfigIAService: CrmApiConfigIAService,
		private cd: ChangeDetectorRef,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {}

	ngOnInit(): void {
		this.formGroup = this.formBuilder.group({
			id: [null],
			codigo: [null],
			empresa: [null],
			titulo: ["", Validators.required],
			tag: [""],
			linkWhatsapp: [""],
			descricao: ["", Validators.required],
			imagemPath: [null],
			periodoInicial: [null],
			periodoFinal: [null],
			template: [null],
			dataCriacao: [new Date().toISOString().split("T")[0]],
		});

		if (this.campanha) {
			const formData = {
				id: this.campanha.id,
				codigoEmpresa: this.campanha.codigoEmpresa,
				empresa: this.campanha.empresa,
				titulo: this.campanha.titulo,
				tag: this.campanha.tag,
				linkWhatsapp: this.campanha.linkWhatsapp,
				descricao: this.campanha.descricao,
				imagemPath: this.campanha.imagemPath,
				periodoInicial: this.campanha.periodoInicial
					? this.parseCustomDate(this.campanha.periodoInicial)
					: null,
				periodoFinal: this.campanha.periodoFinal
					? this.parseCustomDate(this.campanha.periodoFinal)
					: null,
				template: this.campanha.template,
			};

			this.formGroup.patchValue(formData);

			if (
				this.campanha.imagemPath &&
				typeof this.campanha.imagemPath === "string"
			) {
				this.imagemPreviewUrl = this.campanha.imagemPath;
			}
		} else if (this.data && this.data.campanha) {
			this.formGroup.patchValue(this.data.campanha);
			if (this.data.campanha.imagemPath) {
				if (typeof this.data.campanha.imagemPath === "string") {
					this.imagemPreviewUrl = this.data.campanha.imagemPath;
				}
			}
		} else if (this.data && this.data.empresaId) {
			this.formGroup.get("empresa").setValue(this.data.empresaId);
		}
	}

	onFileSelect(event: Event): void {
		const element = event.currentTarget as HTMLInputElement;
		let fileList: FileList | null = element.files;
		if (fileList && fileList[0]) {
			const file = fileList[0];

			const maxSizeInBytes = 5 * 1024 * 1024;
			if (file.size > maxSizeInBytes) {
				this.notificationService.warning(
					"A imagem selecionada é muito grande. O tamanho máximo permitido é de 5MB."
				);
				element.value = '';
				return;
			}

			const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
			if (!allowedTypes.includes(file.type)) {
				this.notificationService.warning(
					"Formato de arquivo não suportado. Por favor, selecione uma imagem (JPEG, PNG, GIF ou WebP)."
				);
				element.value = '';
				return;
			}

			const reader = new FileReader();
			reader.onload = () => {
				const base64String = reader.result as string;
				this.formGroup.patchValue({ imagemPath: base64String });
				this.formGroup.get("imagemPath").updateValueAndValidity();
				this.imagemPreviewUrl = base64String;
				this.cd.detectChanges();
			};
			reader.readAsDataURL(file);
		}
	}

	async saveCampanha(): Promise<void> {
		if (
			(this.campanha && !this.campanha.id) ||
			this.formGroup.get("id").value !== null
		) {
			return this.alterarCampanha();
		}
		const { titulo, descricao, periodoInicial, template } =
			this.formGroup.value;
		if (!titulo || !descricao) {
			this.notificationService.warning(
				"Informe o Título e descrição da campanha."
			);
			return;
		}

		if (!periodoInicial) {
			this.notificationService.warning(
				"Informe o periodo inicial da campanha."
			);
			return;
		}

		const data = this.formGroup.value;

		data.periodoInicial = this.formatDateToBR(new Date(data.periodoInicial));
		data.periodoFinal = data.periodoFinal
			? this.formatDateToBR(new Date(data.periodoFinal))
			: null;

		data.codigoEmpresa = this.empresa;

		this.apiConfigIAService.salvarCampanha(data).subscribe(
			(response) => {
				this.notificationService.success("Campanha salva com sucesso.");
				this.openModal.close(response);
			},
			(error) => {
				this.notificationService.error("Erro ao salvar campanha.");
				console.error("Erro ao salvar campanha:", error);
				this.openModal.close(null);
			}
		);
	}

	async converterImagemParaBase64(url: string): Promise<string> {
		const response = await fetch(url, { mode: "no-cors" });
		const blob = await response.blob();

		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.onloadend = () => resolve(reader.result as string);
			reader.onerror = reject;
			reader.readAsDataURL(blob);
		});
	}

	async alterarCampanha(): Promise<void> {
		const { titulo, descricao, template } = this.formGroup.value;
		if (!titulo || !descricao) {
			this.notificationService.error("Título e descrição são obrigatórios.");
		}
		const data = this.formGroup.value;

		data.periodoInicial = data.periodoInicial
			? this.formatDateToBR(new Date(data.periodoInicial))
			: null;
		data.periodoFinal = data.periodoFinal
			? this.formatDateToBR(new Date(data.periodoFinal))
			: null;
		data.codigoEmpresa = this.empresa;

		if (this.campanha.imagemPath) {
			// Check if the image is already a base64 string (starts with data:image)
			if (
				typeof data.imagemPath === "string" &&
				!data.imagemPath.startsWith("data:image")
			) {
				data.imagemPath = await this.converterImagemParaBase64(
					this.campanha.imagemPath
				);
			}
		}

		this.apiConfigIAService
			.alterarCampanha(this.campanha.id, data)
			.subscribe(
				(response) => {
					this.notificationService.success("Campanha alterada com sucesso.");
					this.openModal.close(response);
				},
				(error) => {
					this.notificationService.error("Erro ao alterar campanha.");
					console.error("Erro ao alterar campanha:", error);
					this.openModal.close(null);
				}
			);
	}

	close(): void {
		if (this.formGroup.valid) {
			this.openModal.close(this.formGroup.value);
		} else {
			this.formGroup.markAllAsTouched();
		}
	}

	cancel(): void {
		this.openModal.close();
	}

	formatDateToBR(date: Date): string {
		const pad = (n: number) => n.toString().padStart(2, "0");
		const dia = pad(date.getDate());
		const mes = pad(date.getMonth() + 1);
		const ano = date.getFullYear();
		const hora = pad(date.getHours());
		const minuto = pad(date.getMinutes());
		const segundo = pad(date.getSeconds());
		return `${dia}/${mes}/${ano} ${hora}:${minuto}:${segundo}`;
	}

	parseCustomDate(input) {
		if (!input) return null;
		const [datePart, timePart] = input.split(" ");
		if (!datePart || !timePart) return null;
		const [day, month, year] = datePart.split("/");
		const [hour, minute, second] = timePart.split(":");
		const parsed = new Date(
			Date.UTC(
				parseInt(year),
				parseInt(month) - 1,
				parseInt(day),
				parseInt(hour),
				parseInt(minute),
				parseInt(second)
			)
		);
		return isNaN(parsed.getTime()) ? null : parsed;
	}
}
