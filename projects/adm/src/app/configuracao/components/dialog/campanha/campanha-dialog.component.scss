.modal-content {
	padding: 20px;
	font-family: "Roboto", sans-serif;
}

.row-title {
	display: flex;
	justify-content: space-between;
	margin-bottom: 24px;

	.column-title {
		.label {
			font-size: 14px;
			font-weight: 500;
			color: #666;
		}

		.value {
			font-size: 16px;
			font-weight: 700;
		}
	}
}

.image-preview {
	padding-top: 20px;
	display: block;
	max-width: 400px;
	max-height: 500px;
	width: auto;
	height: auto;
	object-fit: contain;
}

.grid-container {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 16px;
	margin-bottom: 24px;

	.full-width-input {
		grid-column: span 2;
	}
}
.image-upload-container {
	grid-column: span 2;
	margin-bottom: 16px;

	.image-upload-label {
		display: block;
		font-size: 14px;
		font-weight: 500;
		color: #555;
		margin-bottom: 8px;
	}

	.file-input-wrapper {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;

		.file-input {
			display: none; // Esconde o input nativo
		}

		.file-button {
			display: flex;
			align-items: center;
			gap: 6px;
			background-color: #007bff;
			color: white;
			padding: 6px 12px;
			border: none;
			border-radius: 6px;
			font-weight: 500;
			font-size: 14px;
			cursor: pointer;
			transition: background-color 0.2s ease;

			svg {
				width: 16px;
				height: 16px;
			}

			&:hover {
				background-color: #0056b3;
			}

			&:active {
				background-color: #004494;
			}
		}

		.image-preview {
			min-width: 700px;
			max-width: 700px;
			border: 1px solid #ddd;
			border-radius: 4px;
			margin-top: 8px;
			object-fit: contain;
		}

		.file-info {
			color: #6c757d;
			font-size: 0.8rem;
			margin-top: 5px;
			line-height: 1.2;
		}
	}
}

.date-inputs-container {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 16px;
	margin-bottom: 20px;
}

.footer {
	display: flex;
	justify-content: flex-end;
	padding-top: 16px;
	border-top: 1px solid #eee;

	button {
		min-width: 100px;
	}
}

::ng-deep pacto-cat-form-input {
	.nome {
		visibility: visible !important;
		color: #1d1d1d;
		height: 15px;
	}
}
