import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { map, catchError } from "rxjs/operators";
import { PlanoApiBaseService } from "plano-api";
import { SessionService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class PlanoInativoService {
	constructor(
		private restService: PlanoApiBaseService,
		private sessionService: SessionService
	) {}

	consultarPlanosProximosInativar(): Observable<any> {
		const empresaId = this.sessionService.empresaId;
		const url = `planos/proximos-inativar?empresa=${empresaId}`;

		return this.restService.get(url).pipe(
			catchError((error) => {
				return of(null);
			})
		);
	}

	deveExibirModalPlanosHoje(usuarioId: number): Observable<boolean> {
		const url = `usuario/preferencias/modal-planos/${usuarioId}`;
		return this.restService.get(url).pipe(
			map((result: any) => {
				if (result && result.content !== undefined) {
					return result.content;
				}
				return false;
			}),
			catchError((error) => {
				return of(false);
			})
		);
	}

	naoExibirModalPlanosHoje(usuarioId: number): Observable<any> {
		return this.restService.post(
			`usuario/preferencias/modal-planos/${usuarioId}`,
			{}
		);
	}
}
