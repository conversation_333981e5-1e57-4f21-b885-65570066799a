import { Injectable } from "@angular/core";

import { Observable } from "rxjs";

import { Aditivo } from "./aditivo.model";
import { CadastroAuxApiBaseService } from "./cadastro-aux-api-base.service";
import { CadastroAuxApiModule } from "./cadastro-aux-api.module";

@Injectable({
	providedIn: CadastroAuxApiModule,
})
export class CadastroAuxApiAditivoService {
	constructor(private restApi: CadastroAuxApiBaseService) {}

	public find(id: number | string): Observable<any> {
		return this.restApi.get(`aditivo/${id}`);
	}

	public save(aditivo: Aditivo): Observable<any> {
		return this.restApi.post(`aditivo`, aditivo);
	}

	public delete(id: number): Observable<any> {
		return this.restApi.delete(`aditivo/${id}`);
	}

	public aplicarAditivo(id: number): Observable<any> {
		return this.restApi.post(`aditivo/aplicar/${id}`, {});
	}

	public buscarContratosAditivos(idContrato: number): Observable<any> {
		return this.restApi.get(
			`aditivo/buscar-por-contrato-aditivo/${idContrato}`
		);
	}
}
