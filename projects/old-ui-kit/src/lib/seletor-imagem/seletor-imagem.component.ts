import {
	Component,
	OnInit,
	Input,
	ViewChild,
	ChangeDetectorRef,
	Output,
	EventEmitter,
	OnChanges,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-seletor-imagem",
	templateUrl: "./seletor-imagem.component.html",
	styleUrls: ["./seletor-imagem.component.scss"],
})
export class SeletorImagemComponent implements OnInit, OnChanges {
	@ViewChild("inputImagem", { static: true }) inputImagem;
	@ViewChild("mensagemValidacao", { static: true }) mensagemValidacao;
	@ViewChild("mensagemFormato", { static: true }) mensagemFormato;
	@Input("nome") nome;
	@Input() width = 300;
	@Input() height = 145;
	@Input("url") url;
	@Input("imagemData") imagemData = null;
	@Input() control: FormControl = new FormControl();
	@Input("disabled") disabled: boolean;

	@Output("cleared") cleared: EventEmitter<boolean> = new EventEmitter();

	viewerOpen = false;
	tipo = null;
	enabled = false;
	extensaoArquivo = null;
	megaBytes = 16252928;

	constructor(
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {}

	ngOnChanges(): void {
		this.configureEnabledDisabled();
	}

	setUrl(url) {
		this.url = url;
		this.cd.detectChanges();
	}

	handleFiles(event) {
		if (event.target.files && event.target.files[0]) {
			if (event.target.files[0].size < this.megaBytes) {
				this.verificaTipoImagem(event.target.files[0].type);
				if (this.tipo === "image") {
					const reader = new FileReader();

					reader.onload = (event1: any) => {
						this.url = (<FileReader>event1.target).result.toString();
						const tempImgData = (<FileReader>event1.target).result.toString();
						this.imagemData = tempImgData.replace(
							/^data:image\/\w+;base64,/,
							""
						);
						if (this.control) {
							this.control.setValue(this.imagemData);
						}
						this.cd.detectChanges();
					};
					reader.readAsDataURL(event.target.files[0]);
				} else {
					const mensagemFormato = this.mensagemFormato.nativeElement.innerHTML;
					this.notificationService.error(mensagemFormato);
				}
			} else {
				this.removendoImagem();
				const mensagemValidacao =
					this.mensagemValidacao.nativeElement.innerHTML;
				this.notificationService.error(mensagemValidacao);
			}
		}
	}

	private configureEnabledDisabled() {
		if (this.control) {
			this.enabled = this.control.enabled;
			this.control.registerOnDisabledChange(() => {
				this.enabled = this.control.enabled;
				this.cd.detectChanges();
			});
			this.cd.detectChanges();
		}
	}

	private verificaTipoImagem(dataType: string) {
		this.tipo = null;
		if (dataType.indexOf("image") >= 0) {
			this.tipo = "image";
			this.extensaoArquivo = dataType.replace("image/", ".");
		}
	}

	removendoImagem() {
		this.url = "";
		this.imagemData = null;
		const input: any = document.getElementById(this.nome);
		if (this.control) {
			this.control.setValue(null);
		}
		if (input) {
			input.value = null;
		}
		this.cleared.emit(true);
		this.cd.detectChanges();
	}
}
