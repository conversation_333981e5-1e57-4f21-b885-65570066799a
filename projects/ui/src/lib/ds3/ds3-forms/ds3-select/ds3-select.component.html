<div
	#ds3Select
	(click)="showDropdown()"
	[class.ds3-disabled]="disabled"
	class="ds3-select">
	<div class="ds3-select-body">
		<p [hidden]="isOpen" class="ds3-select-value pct-body1">
			<span *ngIf="selectedValue && !useFullOption">
				{{
					selectedValue
						| findOptionNameBasedOnValue : options : valueKey : nameKey
				}}
			</span>
			<span *ngIf="selectedValue && useFullOption">
				{{ selectedValue[nameKey] }}
			</span>
			<span *ngIf="!selectedValue" class="ds3-select-value-placeholder">
				{{ placeholder }}
			</span>
		</p>
		<input
			#ds3SelectSearch
			(click)="$event.stopPropagation()"
			[formControl]="searchControl"
			[hidden]="!isOpen"
			class="ds3-select-input pct-body1"
			type="search" />
	</div>
	<ng-template cdk-portal #cdkPortal>
		<div class="ds3-select-options">
			<div
				class="ds3-select-refresh"
				*ngIf="canReload"
				(click)="$event.stopPropagation()">
				<div
					ds3Tooltip="Atualizar as opções"
					(click)="$event.stopPropagation()">
					<button
						ds3-icon-button
						[id]="id + '-refresh-option'"
						(click)="
							search(searchControl.value, null, true, true);
							$event.stopPropagation()
						">
						<i class="pct pct-refresh-cw"></i>
					</button>
				</div>
			</div>
			<ng-container *ngFor="let option of options; let index = index">
				<ds3-option
					[value]="option[valueKey]"
					[class.is-selected]="isValueSelected(option[valueKey])"
					[class.is-disabled]="option.disabled"
					[class.is-highlighted]="highlightedIndex === index"
					(click)="selectValue(option[valueKey])">
					{{ option[nameKey] }}
				</ds3-option>
			</ng-container>
			<ng-container *ngIf="!options.length">
				<p class="ds3-select-options-empty">
					{{ valueSearch ? "Nenhuma opção encontrada." : emptyMsg }}
				</p>
			</ng-container>
		</div>
	</ng-template>

	<div #ds3SelectArrow>
		<ds3-select-arrow
			[isOpen]="isOpen"
			[remove]="remove && selectedValue"
			(click)="$event.stopPropagation(); toggleDropdown()"></ds3-select-arrow>
	</div>
</div>
