import {
	AfterViewChecked,
	ChangeDetectorRef,
	Component,
	ElementRef,
	HostListener,
	Input,
	OnDestroy,
	OnInit,
	QueryList,
	ViewChild,
	ViewChildren,
	forwardRef,
	Output,
	EventEmitter,
	ChangeDetectionStrategy,
} from "@angular/core";
import {
	ControlValueAccessor,
	FormControl,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { catchError, debounceTime, map } from "rxjs/operators";
import { Overlay, OverlayConfig, OverlayRef } from "@angular/cdk/overlay";
import { CdkPortal } from "@angular/cdk/portal";
import { Observable, Subscription, of } from "rxjs";
import { HttpClient } from "@angular/common/http";

export interface Ds3SelectOptionModel {
	value: string;
	name: string;
}

export type Ds3SelectFilterParamBuilder = (filter: string) => {
	[param: string]: string | string[];
};

export type Ds3SelectFilterResponseParser = (result: any) => any[];

@Component({
	selector: "ds3-select-multi",
	templateUrl: "./ds3-select-multi.component.html",
	styleUrls: ["./ds3-select-multi.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3SelectMultiComponent),
			multi: true,
		},
	],
})
export class Ds3SelectMultiComponent
	implements OnInit, AfterViewChecked, OnDestroy, ControlValueAccessor
{
	@ViewChild("ds3Select", { static: true })
	selectElement: ElementRef;

	@ViewChild(CdkPortal, { static: true }) portal!: CdkPortal;
	@ViewChildren("ds3SelectHiddenChipsList")
	selectHiddenChipsListElement: QueryList<ElementRef>;

	selectedValues: Array<any> = [];
	notSelectedValues: any[] = [];
	id: string = "";

	public isChipsListOverflowing = false;
	public isSelectedOpen = false;
	public isNotSelectedOpen = true;
	public isOpen: boolean = false;
	public searchControl = new FormControl();
	public tempOptions;
	public searchedOptions = [];
	public isSearching;
	public isLoading;
	shouldFocusArrow;

	private onChanged: (value: any) => void = (v) => {
		/* NOOP */
	};
	private onTouched: () => void = () => {
		/* NOOP */
	};

	@Input()
	public placeholder?: string = "Selecione um item";

	@Input()
	public options: any[] = [];

	@Input()
	public disabled?: boolean;

	private overlayRef!: OverlayRef;

	@Input()
	public valueKey: string = "value";

	@Input()
	public nameKey: string = "name";

	@Input()
	public sortKey: string;

	@Input()
	endpointUrl;

	@Input()
	paramBuilder: Ds3SelectFilterParamBuilder;

	@Input()
	responseParser: Ds3SelectFilterResponseParser;

	@Input()
	additionalFilters: any;

	@Input()
	initOption: any;

	@Input()
	addEmptyOption = false;

	public highlightedIndex: number = -1;

	/*
	 * Utilizar quando é necessário que o valor do control seja o objeto da opção selecionada.
	 * Como o select foi criado para sempre enviar só o que é definido pela valuKey, alguns casos isso
	 * acaba atrapalhando, sendo necessário ficar iterando no array das opções pra trabalhar com algum dado.
	 *
	 * O padrão é false para não afetar o que já existe
	 * */
	@Input()
	useValueAsObject: boolean = false;

	@Output() opened: EventEmitter<any> = new EventEmitter<any>();
	@Output() search: EventEmitter<any> = new EventEmitter<any>();
	@Output() itemRemoved: EventEmitter<any> = new EventEmitter<any>();

	private searchSubscription: Subscription;

	private fetchDataCache = new Map<string, any>();
	private lastFetchTime: number = 0;
	private readonly CACHE_DURATION: number = 30000; // 30s
	private readonly TIME_TO_ENABLE_RELOAD: number = 30000;

	canReload: boolean = false;
	private timeToEnableReloadInterval;

	constructor(
		private overlay: Overlay,
		private http: HttpClient,
		private cd: ChangeDetectorRef,
		private elementRef: ElementRef,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.id = this.elementRef.nativeElement.id;
		if (this.options) {
			this.updateNotSelectedValues(this.selectedValues, this.options);
			this.tempOptions = [...this.options];
		}
		this.searchControl.valueChanges
			.pipe(debounceTime(300))
			.subscribe((term) => this.onSearch(term));
		this.onSearch(null, this.selectedValues);
	}

	ngAfterViewChecked(): void {
		this.updateNotSelectedValues(this.selectedValues, this.options);
		this.tempOptions = Array.isArray(this.options) ? [...this.options] : [];
		this.checkIfIsChipsListOverflowing();
	}

	ngOnDestroy(): void {
		if (this.isOpen) {
			this.hide();
		}
		this._clearReloadInterval();
		this.fetchDataCache.clear();
	}

	private _resetReloadInterval(canReload) {
		this.canReload = canReload;
		this._clearReloadInterval();
		this._initReloadInterval();
	}

	private _initReloadInterval() {
		if (!this.endpointUrl) {
			return;
		}
		this.timeToEnableReloadInterval = setInterval(() => {
			if (this.canReload) {
				this._clearReloadInterval();
				return;
			}
			this.canReload = true;
			this.cd.markForCheck();
		}, this.TIME_TO_ENABLE_RELOAD);
	}

	private _clearReloadInterval() {
		clearInterval(this.timeToEnableReloadInterval);
		this.timeToEnableReloadInterval = null;
	}

	toggleDropdown(): void {
		if (this.isOpen) {
			this.hide();
			return;
		}

		this.showDropdown();
	}

	showDropdown(): void {
		if (this.disabled || this.isOpen) {
			return;
		}

		if (!this.overlayRef) {
			this.overlayRef = this.overlay.create(this.getOverlayConfig());
		}
		this.overlayRef.attach(this.portal);
		this.syncWidth();
		this.onSearch(null, null, false);
		this.isOpen = true;
		this.highlightedIndex = -1;
		this.opened.emit(this.isOpen);
	}

	private hide(): void {
		this.overlayRef.detach();
		this.isOpen = false;
		this.opened.emit(this.isOpen);
		this.shouldFocusArrow = true;
		setTimeout(() => (this.shouldFocusArrow = false), 100);
	}

	@HostListener("document:click", ["$event", "$event.target"])
	clickedOutside(e: MouseEvent, targetElement: HTMLElement) {
		if (!this.isOpen) {
			return;
		}

		if (!this.isDescendant(this.selectElement.nativeElement, targetElement)) {
			this.hide();
		}
	}

	private getOverlayConfig(): OverlayConfig {
		const positionStrategy = this.overlay
			.position()
			.flexibleConnectedTo(this.selectElement.nativeElement)
			.withPush(true)
			.withPositions([
				{
					originX: "start",
					originY: "bottom",
					overlayX: "start",
					overlayY: "top",
					offsetY: 4,
				},
				{
					originX: "end",
					originY: "bottom",
					overlayX: "end",
					overlayY: "top",
					offsetY: 4,
				},
				{
					originX: "start",
					originY: "top",
					overlayX: "start",
					overlayY: "bottom",
					offsetY: -4,
				},
				{
					originX: "end",
					originY: "top",
					overlayX: "end",
					overlayY: "bottom",
					offsetY: -4,
				},
			]);

		const scrollStrategy = this.overlay.scrollStrategies.reposition();
		return new OverlayConfig({
			positionStrategy: positionStrategy,
			scrollStrategy: scrollStrategy,
			hasBackdrop: false,
			backdropClass: "cdk-overlay-transparent-backdrop",
		});
	}

	private syncWidth(): void {
		if (!this.overlayRef) {
			return;
		}
		const refRectWidth =
			this.selectElement.nativeElement.getBoundingClientRect().width;
		this.overlayRef.updateSize({ width: refRectWidth });
	}

	toggleSelectedAccordion() {
		if (this.selectedValues.length == 0) {
			return;
		}
		this.isSelectedOpen = !this.isSelectedOpen;
		console.log("this.isSelectedOpen", this.isSelectedOpen);
	}

	toggleNotSelectedAccordion() {
		if (this.notSelectedValues.length == 0) {
			return;
		}
		this.isNotSelectedOpen = !this.isNotSelectedOpen;
	}

	isTemplate(value: any) {
		const type = typeof value;
		if (type === "undefined") {
			return false;
		} else if (type === "string") {
			return false;
		} else {
			return true;
		}
	}

	onSearch(
		term,
		initValue = null,
		reload = true,
		refreshClick: boolean = false
	) {
		if (this.endpointUrl) {
			this.isLoading = true;
			this.searchSubscription = this.fetchData(
				term,
				reload,
				refreshClick
			).subscribe((result) => {
				this.isLoading = false;
				this.options = result;

				if (this.initOption && this.options && Array.isArray(this.options)) {
					const existOption = this.options.some(
						(option) => option[this.valueKey] === this.initOption[this.valueKey]
					);
					if (!existOption) {
						this.options.push(this.initOption);
					}
				}

				if (this.addEmptyOption) {
					const emptyOption: any = {};
					emptyOption[this.valueKey] = null;
					emptyOption[this.nameKey] = "-";
					this.options.unshift(emptyOption);
				}
				if (initValue && initValue.length > 0) {
					let valueToSelect = initValue;
					if (initValue instanceof Object) {
						valueToSelect = initValue[this.valueKey];
					}
					const op = this.options.find(
						(option) => option[this.valueKey] === valueToSelect
					);
					const value = op[this.valueKey];
					this.selectValue(value, op);
				}
				this.cd.detectChanges();
			});
			return;
		}

		if (this.tempOptions) {
			const filtered = this.tempOptions.filter((x) => {
				const lowercaseTerm = term ? term.toLowerCase() : term;
				return (
					x[this.nameKey].toLowerCase().includes(lowercaseTerm) ||
					!lowercaseTerm
				);
			});
			this.searchedOptions = filtered;
		}

		if (
			!this.endpointUrl &&
			(!this.searchedOptions || this.searchedOptions.length === 0)
		) {
			this.search.emit({ term });
		}

		this.highlightedIndex = term != null && term != "" ? 0 : -1;
	}

	private isDescendant(parentElement, childElement) {
		let node = childElement.parentNode;
		if (parentElement === childElement) {
			return true;
		} else {
			while (node !== null) {
				if (node === parentElement) {
					return true;
				}
				node = node.parentNode;
			}
			return false;
		}
	}

	private fetchData(
		term: string,
		reload = false,
		refreshClick: boolean = false
	): Observable<any> {
		let cacheKey = `${this.id}`;
		if (this.additionalFilters && this.additionalFilters.length > 0) {
			cacheKey += `-${JSON.stringify(this.additionalFilters)}`;
		}
		const now = Date.now();

		const currentDataCache = this.fetchDataCache.get(cacheKey);
		const lowercaseTerm = term ? term.toLowerCase() : term;

		if (
			reload &&
			refreshClick &&
			now - this.lastFetchTime < this.TIME_TO_ENABLE_RELOAD
		) {
			this.notificationService.warning(
				`Houve uma atualização nos últimos ${
					this.TIME_TO_ENABLE_RELOAD / 1000
				}s, aguarde um momento para atualizar novamente!`
			);
			this._resetReloadInterval(false);
			return of(currentDataCache || []);
		}

		if (
			currentDataCache &&
			now - this.lastFetchTime < this.CACHE_DURATION &&
			!reload
		) {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
			let filtered = currentDataCache.filter((x) => {
				if (
					x[this.nameKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm ||
					lowercaseTerm.length === 0
				) {
					return x;
				}
			});
			if (filtered.length !== 0) {
				filtered = this._sortValues(filtered);
				return of(filtered);
			}
		}

		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};

		if (this.additionalFilters) {
			const filters = JSON.parse(params.filters.toString());
			Object.keys(this.additionalFilters).forEach((key) => {
				filters[key] = this.additionalFilters[key];
			});
			params.filters = JSON.stringify(filters);
		}

		const data = this.http.get(url, { params });

		return data.pipe(
			// debounceTime(300), // Add debounce
			map((result) => {
				this._resetReloadInterval(false);
				const parsedResult: any = this.responseParser
					? this.responseParser(result)
					: result;
				const resultContent = parsedResult.content
					? parsedResult.content
					: parsedResult;
				// Cache the result
				if (this.fetchDataCache.has(cacheKey)) {
					let actualResult = this.fetchDataCache.get(cacheKey);
					const actualResultContent = actualResult.map((v) => v[this.valueKey]);

					resultContent.forEach((rc) => {
						if (!actualResultContent.includes(rc[this.valueKey])) {
							actualResult.push(rc);
						}
					});
					actualResult = this._sortValues(actualResult);

					this.fetchDataCache.set(cacheKey, actualResult);
				} else {
					this.fetchDataCache.set(cacheKey, resultContent);
				}
				this.lastFetchTime = now;
				return resultContent;
			}),
			catchError((err) => {
				return of([]);
			})
		);
	}

	private _sortValues(data) {
		const sortKey = this.sortKey ? this.sortKey : this.nameKey;
		return data.sort((a, b) => {
			const aValue = a[sortKey] ? a[sortKey] : "";
			const bValue = b[sortKey] ? b[sortKey] : "";
			if (aValue > bValue) {
				return 1;
			} else if (aValue < bValue) {
				return -1;
			} else {
				return 0;
			}
		});
	}
	selectValue(value: string, option?: any): void {
		if (this.useValueAsObject) {
			this.writeValue(option);
		} else {
			this.writeValue(value);
		}

		this._updateValues();
	}

	private _updateValues() {
		this.onChanged(this.selectedValues);
		this.updateNotSelectedValues(this.selectedValues, this.options);

		this.checkIfIsChipsListOverflowing();

		this.isSelectedOpen = true;
		if (this.selectedValues.length === 0) {
			this.isSelectedOpen = false;
		}
		this.onTouched();
		this.cd.markForCheck();
	}

	discardValue(value: string, option?): void {
		const removedArray = this.removeValue(value, option);
		this.onChanged(removedArray);
		this.updateNotSelectedValues(this.selectedValues, this.options);

		this.checkIfIsChipsListOverflowing();
		this.itemRemoved.emit({ value, option });
		this.onTouched();
	}

	toggleSelectValue(value: string, option?) {
		this.isValueSelected(value, option)
			? this.discardValue(value, option)
			: this.selectValue(value, option);
	}

	updateNotSelectedValues(selectedValues: Array<any>, allValues: any[]) {
		let notSelectedOptions = [];
		if (!allValues) {
			this.notSelectedValues = notSelectedOptions;
			return notSelectedOptions;
		}
		if (this.useValueAsObject) {
			const selVal = selectedValues.map((v) => v[this.valueKey]);
			notSelectedOptions = allValues.filter(
				(item) => !selVal.includes(item[this.valueKey])
			);

			this.notSelectedValues = notSelectedOptions;
			return notSelectedOptions;
		}
		notSelectedOptions = allValues.filter(
			(item) => !selectedValues.includes(item[this.valueKey])
		);
		this.notSelectedValues = notSelectedOptions;
		return notSelectedOptions;
	}

	registerOnChange(angularProvidedFunction: any): void {
		this.onChanged = angularProvidedFunction;
	}

	registerOnTouched(angularProvidedFunction: any): void {
		this.onTouched = angularProvidedFunction;
	}

	setDisabledState(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	writeValue(value: any): void {
		if (value === null || value === undefined) {
			return;
		}

		if (Array.isArray(value)) {
			/**
			 * Aqui reseta o array para que caso tenha alguma remoção programática
			 * seja recriado os itens do array.
			 */
			this.selectedValues = [];
			if (value.length === 0) {
				this._updateValues();
			}
			value.forEach((item) => {
				setTimeout(() => {
					this.selectValue(item, item);
					this.cd.detectChanges();
				});
			});
			return;
		}

		if (this.useValueAsObject) {
			if (!this.isValueSelected(undefined, value)) {
				this.selectedValues.push(value);
			}
		} else {
			if (!this.isValueSelected(value)) {
				this.selectedValues.push(value);
			}
		}
	}

	removeValue(value: string, option?): Array<string> {
		if (value === null || value === undefined) {
			return;
		}
		if (this.isValueSelected(value, option)) {
			let arrayRemoved;
			if (this.useValueAsObject) {
				arrayRemoved = this.selectedValues.filter(
					(v) => v[this.valueKey] !== option[this.valueKey]
				);
			} else {
				arrayRemoved = this.selectedValues.filter((item) => item !== value);
			}
			this.selectedValues = arrayRemoved;
			return arrayRemoved;
		}
	}

	isValueSelected(value: string, option?): boolean {
		if (this.useValueAsObject) {
			return !!this.selectedValues.find(
				(v) => v[this.valueKey] === option[this.valueKey]
			);
		}
		return this.selectedValues.includes(value);
	}

	checkIfIsChipsListOverflowing() {
		setTimeout(() => {
			this.isChipsListOverflowing =
				this.selectHiddenChipsListElement.first.nativeElement.offsetWidth >
				this.selectElement.nativeElement.offsetWidth - 50;
		});
	}

	onKeyEvent(event: KeyboardEvent) {
		if (!this.isOpen) return;

		const optionsList = this.navigableOptions;

		if (this.highlightedIndex >= optionsList.length) {
			this.highlightedIndex = optionsList.length - 1;
		}
		if (this.highlightedIndex < 0 && optionsList.length > 0) {
			this.highlightedIndex = 0;
		}

		switch (event.key) {
			case "ArrowDown":
				event.preventDefault();
				if (optionsList.length > 0) {
					this.highlightedIndex =
						(this.highlightedIndex + 1) % optionsList.length;
					this.scrollToHighlighted();
				}
				break;
			case "ArrowUp":
				event.preventDefault();
				if (optionsList.length > 0) {
					this.highlightedIndex =
						(this.highlightedIndex - 1 + optionsList.length) %
						optionsList.length;
					this.scrollToHighlighted();
				}
				break;
			case "Enter":
				if (!this.isOpen) return;

				event.preventDefault();
				if (this.highlightedIndex >= 0 && optionsList[this.highlightedIndex]) {
					const option = this.navigableOptions[this.highlightedIndex];
					if (!this.isValueSelected(option[this.valueKey], option)) {
						this.selectValue(option[this.valueKey]);
					} else {
						this.discardValue(option[this.valueKey]);
					}
					this.cd.detectChanges();
				}
				break;
			case "Escape":
				event.preventDefault();
				this.hide();
				break;
		}
	}

	get navigableOptions() {
		if (this.searchControl.value) {
			return this.searchedOptions;
		}
		if (this.useValueAsObject) {
			return [...this.selectedValues, ...this.notSelectedValues];
		}
		const selectedObjs = this.selectedValues
			.map((val) => this.options.find((opt) => opt[this.valueKey] === val))
			.filter(Boolean);
		return [...selectedObjs, ...this.notSelectedValues];
	}

	private scrollToHighlighted() {
		setTimeout(() => {
			const optionElements = document.querySelectorAll(
				".ds3-select-multiple-list ds3-option"
			);
			if (optionElements[this.highlightedIndex]) {
				(optionElements[this.highlightedIndex] as HTMLElement).scrollIntoView({
					block: "nearest",
				});
			}
		}, 0);
	}

	trackByOption(index: number, option: any): any {
		return option ? option[this.valueKey] : index;
	}

	trackByValue(index: number, value: any): any {
		return value || index;
	}
}
