import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChild,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	NgZone,
	OnDestroy,
	OnInit,
	Output,
	Pipe,
	PipeTransform,
	QueryList,
	ViewChild,
	ViewChildren,
	ViewContainerRef,
} from "@angular/core";
import {
	ControlContainer,
	ControlValueAccessor,
	FormControl,
	FormControlDirective,
	NG_VALUE_ACCESSOR,
} from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { PactoSelectSize } from "../../../../../forms/cat-form-select/cat-form-select.component";
import { fromEvent, Observable, of, Subscription } from "rxjs";
import {
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	SelectItemDirective,
} from "../../../../cat-select-filter/cat-select-filter.component";
import { HttpClient } from "@angular/common/http";
import { PactoUtilService } from "../../../../../pacto-util.service";
import { catchError, debounceTime, map } from "rxjs/operators";
import { IInfiniteScrollEvent } from "ngx-infinite-scroll";

@Pipe({
	name: "columnSelectOptionLabel",
})
export class ColumnSelectOptionLabel implements PipeTransform {
	transform(
		option: any,
		labelFn: (item: any) => string,
		options: Array<any>,
		idKey: string,
		labelKey: string,
		control: FormControl,
		placeholder: string
	): any {
		if (labelFn) {
			return labelFn(option);
		} else {
			if (option[idKey]) {
				let value = "-";
				value = options.find((opt) => opt[idKey] === option[idKey]);
				if (value) {
					return value[labelKey];
				}
				if (control && control.value) {
					return control.value[labelKey];
				}
				return placeholder;
			}
			return option[labelKey] ? option[labelKey] : placeholder;
		}
	}
}

@Component({
	selector: "pacto-column-select",
	templateUrl: "./column-select.component.html",
	styleUrls: ["./column-select.component.scss"],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: ColumnSelectComponent,
			multi: true,
		},
	],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ColumnSelectComponent
	implements OnInit, AfterViewInit, OnDestroy, ControlValueAccessor
{
	@Input() formControlName: string;
	@ViewChild(FormControlDirective, { static: true })
	formControlDirective: FormControlDirective;
	@Input() formControl: FormControl;
	@Input() options: Array<any>;
	@Input() optionsSubscription: Observable<any>;
	@Input() labelKey = "label";

	@Input()
	public sortKey: string;

	@Input() id: string;
	@Input() idKey = "id";
	@Input() showFilter = false;
	@Input() showAddBtn = false;
	@Input() errorMsg = null;
	/**
	 * Full url of endpoint without any GET params.
	 *
	 */
	@Input() endpointUrl;
	@Input() paramBuilder: SelectFilterParamBuilder;
	@Input() resposeParser: SelectFilterResponseParser;
	@Input() placeholder = "-";
	@Input() label;
	@Input() labelFn: (item: any) => string;
	@Input() imageKey;
	@Input() hasClearAction = false;
	@Input() size: PactoSelectSize = PactoSelectSize.NORMAL;
	@Input() addEmptyOption = false;
	@Input() customOption = undefined;
	@Input() infiniteScrollEnabled = false;
	@Input() elementsSize = 10;

	@Output() optionChange: EventEmitter<any> = new EventEmitter<any>();
	@Output() addEvent: EventEmitter<any> = new EventEmitter<any>();
	@ViewChild("currentElRef", { static: true })
	currentElRef: ElementRef;
	@ViewChild("filter", { static: false }) filter;
	@ViewChild("arrow", { static: true }) arrow;

	@ContentChild(SelectItemDirective, { static: false })
	selectItem: SelectItemDirective;

	page = 0;

	heightOfOptions = 0;

	private searchSubscription: Subscription;

	filterFC: FormControl = new FormControl("");
	optionsOpen = false;
	loading = false;
	destroyed = false;
	optionSubscribed: Subscription;
	private _scrollSubscription: Subscription;

	@Input() disabledControl = false;
	@Input() showEmptyMessage = true;

	private fetchDataCache = new Map<string, any>();
	private lastFetchTime: number = 0;
	private readonly CACHE_DURATION: number = 30000; // 30s
	private readonly TIME_TO_ENABLE_RELOAD: number = 30000;

	canReload: boolean = false;
	private timeToEnableReloadInterval;

	get control(): any {
		return (
			this.formControl ||
			this.controlContainer.control.get(this.formControlName)
		);
	}

	constructor(
		private compRef: ElementRef,
		private http: HttpClient,
		private cd: ChangeDetectorRef,
		private util: PactoUtilService,
		private controlContainer: ControlContainer,
		private vcr: ViewContainerRef,
		private zone: NgZone,
		private notificationService: SnotifyService
	) {}

	get small() {
		return this.size === PactoSelectSize.SMALL;
	}

	get isInvalid() {
		return this.control && this.control.touched && !this.control.valid;
	}

	get error() {
		return this.isInvalid && !this.control.disabled;
	}

	get currentOption() {
		if (this.control) {
			return this.control.value;
		} else {
			return null;
		}
	}

	get filteredOptions() {
		if (this.endpointUrl) {
			return this.options;
		} else {
			return this.options.filter((item) => {
				const token = item[this.labelKey];
				let filter = this.filterFC.value;
				filter = filter ? filter : "";
				const regex = new RegExp(`${filter}`, "gi");
				return regex.test(token);
			});
		}
	}

	getOptionLabel(option: any): string {
		if (this.labelFn) {
			return this.labelFn(option);
		} else {
			if (option[this.idKey]) {
				let value = "-";
				value = this.options.find(
					(opt) => opt[this.idKey] === option[this.idKey]
				);
				if (value) {
					return value[this.labelKey];
				}
				if (this.control && this.control.value) {
					return this.control.value[this.labelKey];
				}
				return this.placeholder;
			}
			return option[this.labelKey] ? option[this.labelKey] : this.placeholder;
		}
	}

	ngOnInit() {
		if (!this.options) {
			this.options = [];
		}
		if (this.disabledControl) {
			this.control.disable();
		} else {
			this.control.enable();
		}
		this.validateId();

		/**
		 * Cancel result callback
		 */
		this.filterFC.valueChanges.subscribe(() => {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
		});

		/**
		 * Trigger search after delay
		 */
		this.filterFC.valueChanges.pipe(debounceTime(500)).subscribe((term) => {
			this.search(term);
		});

		if (this.optionsSubscription) {
			this.optionSubscribed = this.optionsSubscription.subscribe((options) => {
				this.options = options;
				this.cd.detectChanges();
			});
		}
		this._initReloadInterval();
	}

	ngOnDestroy() {
		this.destroyed = true;
		if (this.optionSubscribed) {
			this.optionSubscribed.unsubscribe();
		}

		if (this._scrollSubscription) {
			this._scrollSubscription.unsubscribe();
		}

		this._clearReloadInterval();
		this.fetchDataCache.clear();
	}

	ngAfterViewInit() {
		let element = document.querySelector(".menu-content");
		this._scrollSubscription = fromEvent(element, "scroll").subscribe(() => {
			this.optionsOpen = false;
			this.cd.detectChanges();
		});
	}

	clearHandler(event: MouseEvent) {
		if (this.control.enabled) {
			event.stopPropagation();
			this.control.reset(null);
		}
	}

	currentClickHandler() {
		if (this.control.enabled) {
			if (!this.optionsOpen) {
				this.open();
			} else {
				this.optionsOpen = false;
			}
		}
		if (this.control.enabled) {
			this.arrow.nativeElement.classList.contains("open")
				? this.arrow.nativeElement.classList.remove("open")
				: this.arrow.nativeElement.classList.add("open");
		}
	}

	selectOptionHandler(option?) {
		if (option) {
			this.control.setValue(option);
			this.optionChange.emit(option);
		}
		this.optionsOpen = false;
	}

	@HostListener("document:click", ["$event"])
	clickDocumentHandler($event) {
		if (!this.isComponentClick($event)) {
			this.optionsOpen = false;
			this.page = 0;
		}
	}

	private open() {
		this.filterFC.setValue("", { emitEvent: false });
		this.search(null, false, false);
		setTimeout(() => {
			if (this.filter) {
				this.filter.nativeElement.focus();
			}
		}, 200);
		this.optionsOpen = true;
	}

	search(
		term,
		concatOptions: boolean = false,
		reload = true,
		refreshClick: boolean = false
	) {
		if (this.endpointUrl) {
			this.loading = true;
			// this.cd.detectChanges();
			this.searchSubscription = this.fetchData(
				term,
				reload,
				refreshClick
			).subscribe((result) => {
				this.loading = false;
				if (!concatOptions) {
					this.options = result;
				} else {
					this.options.push(...result);
				}
				if (this.addEmptyOption) {
					const emptyOption: any = {};
					emptyOption[this.idKey] = null;
					emptyOption[this.labelKey] = "-";
					this.options.unshift(emptyOption);
				}
				if (this.customOption !== undefined) {
					this.options.unshift(this.customOption);
				}
				this.cd.detectChanges();
			});
		} else {
			if (this.addEmptyOption) {
				const emptyOption: any = {};
				emptyOption[this.idKey] = null;
				emptyOption[this.labelKey] = "-";
				if (!this.options.find((option) => option[this.idKey] === null)) {
					this.options.unshift(emptyOption);
				}
			}
			this.cd.detectChanges();
		}
	}

	private fetchData(
		term: string,
		reload = false,
		refreshClick: boolean = false
	): Observable<any> {
		const cacheKey = `${this.id}`;
		const now = Date.now();

		const currentDataCache = this.fetchDataCache.get(cacheKey);
		const lowercaseTerm = term ? term.toLowerCase() : term;

		if (
			reload &&
			refreshClick &&
			now - this.lastFetchTime < this.TIME_TO_ENABLE_RELOAD
		) {
			this.notificationService.warning(
				`Houve uma atualização nos últimos ${
					this.TIME_TO_ENABLE_RELOAD / 1000
				}s, aguarde um momento para atualizar novamente!`
			);
			this._resetReloadInterval(false);
			return of(currentDataCache || []);
		}

		if (
			currentDataCache &&
			now - this.lastFetchTime < this.CACHE_DURATION &&
			!reload
		) {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
			let filtered = currentDataCache.filter((x) => {
				if (
					x[this.labelKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm ||
					lowercaseTerm.length === 0
				) {
					return x;
				}
			});
			if (filtered.length !== 0) {
				filtered = this._sortValues(filtered);
				return of(filtered);
			}
		}
		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};
		if (this.infiniteScrollEnabled) {
			params.page = `${this.page++}`;
			params.size = `${this.elementsSize}`;
		}
		const data = this.http.get(url, { params });

		return data.pipe(
			map((result) => {
				this._resetReloadInterval(false);
				const parsedResult: any = this.resposeParser
					? this.resposeParser(result)
					: result;
				const resultContent = parsedResult.content
					? parsedResult.content
					: parsedResult;
				// Cache the result
				if (this.fetchDataCache.has(cacheKey)) {
					let actualResult = this.fetchDataCache.get(cacheKey);
					const actualResultContent = actualResult.map((v) => v[this.idKey]);

					resultContent.forEach((rc) => {
						if (!actualResultContent.includes(rc[this.idKey])) {
							actualResult.push(rc);
						}
					});

					actualResult = this._sortValues(actualResult);

					this.fetchDataCache.set(cacheKey, actualResult);
				} else {
					this.fetchDataCache.set(cacheKey, resultContent);
				}
				this.lastFetchTime = now;
				return resultContent;
			}),
			catchError(() => {
				if (this.resposeParser) {
					return of(this.resposeParser([]));
				} else {
					return of([]);
				}
			})
		);
	}

	private validateId() {
		if (!this.id) {
			this.id = "cat-select-filter-id-" + Math.trunc(Math.random() * 1000);
		}
	}

	private isSelectAreaClick($event: MouseEvent) {
		return this.isComponentClick($event) && !this.isOptionAreaClick($event);
	}

	private isComponentClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.compRef.nativeElement,
			$event.target as Element
		);
	}

	private isOptionAreaClick($event: MouseEvent) {
		/*return this.util.isDescendant(
            this.selectArea.nativeElement,
            $event.target as Element
        );*/
		return true;
	}

	add() {
		this.addEvent.emit();
	}

	registerOnTouched(fn: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.registerOnTouched(fn);
		}
	}

	registerOnChange(fn: any): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.registerOnChange(fn);
		}
	}

	writeValue(obj: any): void {
		if (this.formControlDirective) {
			const objMin: any = {};
			if (obj) {
				objMin[this.idKey] = obj[this.idKey];
				objMin[this.labelKey] = obj[this.labelKey];
			}
			if (!this.destroyed) {
				// this.cd.detectChanges();
			}
			this.formControlDirective.valueAccessor.writeValue(objMin);
		}
	}

	setDisabledState(isDisabled: boolean): void {
		if (this.formControlDirective) {
			this.formControlDirective.valueAccessor.setDisabledState(isDisabled);
			if (isDisabled) {
				this.optionsOpen = false;
			}
		}
	}

	onDropdownScrolled(event: IInfiniteScrollEvent) {
		this.search(this.filterFC.value || null, true);
	}

	private _resetReloadInterval(canReload) {
		this.canReload = canReload;
		this._clearReloadInterval();
		this._initReloadInterval();
	}

	private _initReloadInterval() {
		if (!this.endpointUrl) {
			return;
		}
		this.timeToEnableReloadInterval = setInterval(() => {
			if (this.canReload) {
				this._clearReloadInterval();
				return;
			}
			this.canReload = true;
			this.cd.markForCheck();
		}, this.TIME_TO_ENABLE_RELOAD);
	}

	private _clearReloadInterval() {
		clearInterval(this.timeToEnableReloadInterval);
		this.timeToEnableReloadInterval = null;
	}

	private _sortValues(data) {
		const sortKey = this.sortKey ? this.sortKey : this.labelKey;
		return data.sort((a, b) => {
			const aValue = a[sortKey] ? a[sortKey] : "";
			const bValue = b[sortKey] ? b[sortKey] : "";
			if (aValue > bValue) {
				return 1;
			} else if (aValue < bValue) {
				return -1;
			} else {
				return 0;
			}
		});
	}
}
