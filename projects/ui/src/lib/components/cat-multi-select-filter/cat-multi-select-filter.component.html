<input [formControl]="control" class="cat-select-filter" type="hidden" />

<div class="host-wrapper">
	<!-- LABEL -->
	<div *ngIf="label" class="pacto-label">
		{{ label }}
	</div>

	<div
		#currentElRef
		(click)="currentClickHandler($event)"
		[ngClass]="{
			opened: optionsOpen,
			error: showError
		}"
		class="current-value"
		id="{{ id }}">
		<img
			*ngIf="!nofOptions"
			class="double-arrow"
			src="pacto-ui/images/double-arrow.svg" />

		<div
			(click)="pillClickHandler($event)"
			*ngFor="let option of currentOptions; let index = index"
			[@inOut]
			class="selected-option">
			<pacto-cat-person-avatar
				*ngIf="imageKey"
				[diameter]="22"
				[uri]="option[imageKey]"></pacto-cat-person-avatar>
			<div class="value">
				{{ option[labelKey] }}
			</div>
			<i
				(click)="removeOptionHandler(index, $event)"
				class="pct pct-x"
				id="option-{{ option[labelKey] }}-remove"></i>
		</div>

		<div class="well-footer"></div>

		<div *ngIf="!currentOptions.length" class="select-placeholder">
			{{ placeholder }}
		</div>
	</div>

	<div class="footer-multi-select">
		<i
			(click)="clearAllHandler($event)"
			*ngIf="nofOptions"
			class="pct pct-trash-2 clear-icon"></i>

		<!-- OPTIONS -->
		<div #selectArea [hidden]="!optionsOpen" class="options">
			<div class="d-flex align-items-center">
				<input
					#filter
					[autocomplete]="autocomplete"
					[formControl]="filterFC"
					[placeholder]="'form.filter' | translate"
					class="filter-input"
					id="{{ this.id + '-filter' }}" />
				<div
					class="cat-select-refresh"
					*ngIf="canReload"
					(click)="$event.stopPropagation()">
					<div
						ds3Tooltip="Atualizar as opções"
						(click)="$event.stopPropagation()">
						<button
							ds3-icon-button
							[id]="id + '-refresh-option'"
							(click)="
								search(filterFC.value, true, true); $event.stopPropagation()
							">
							<i class="pct pct-refresh-cw"></i>
						</button>
					</div>
				</div>
			</div>

			<div
				*ngIf="options && options.length"
				[maxHeight]="'250px'"
				class="scroll-container"
				pactoCatSmoothScroll>
				<div
					(click)="selectOptionHandler(option, $event)"
					*ngFor="let option of filteredOptions; let index = index"
					[@inOut]
					class="option"
					id="{{ this.id + '-' + index }}">
					<pacto-cat-person-avatar
						*ngIf="imageKey"
						[diameter]="24"
						[uri]="option[imageKey]"></pacto-cat-person-avatar>
					<span>{{ option[labelKey] }}</span>
				</div>
			</div>

			<div *ngIf="!loading && !filteredOptions.length" class="empty-state">
				Nenhum resultado.
			</div>
		</div>
	</div>
</div>
