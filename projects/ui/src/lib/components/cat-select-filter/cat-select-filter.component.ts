import { HttpClient } from "@angular/common/http";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChild,
	Directive,
	ElementRef,
	EventEmitter,
	HostListener,
	Input,
	OnDestroy,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { SnotifyService } from "ng-snotify";

import { Observable, of, Subscription } from "rxjs";
import { catchError, debounceTime, map } from "rxjs/operators";
import { PactoSelectSize } from "../../forms/pacto-forms-api";
import { PactoUtilService } from "../../pacto-util.service";

export type SelectFilterParamBuilder = (filter: string) => {
	[param: string]: string | string[];
};

export type SelectFilterResponseParser = (result: any) => any[];

// tslint:disable-next-line:directive-selector
@Directive({ selector: "select-item" })
export class SelectItemDirective {
	@ContentChild(TemplateRef, { static: false })
	templateRef: TemplateRef<any>;
	@Input() id: string;
}

@Component({
	selector: "pacto-cat-select-filter",
	templateUrl: "./cat-select-filter.component.html",
	styleUrls: ["./cat-select-filter.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CatSelectFilterComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	@Input() id;
	@ViewChild("optionLabel", { static: false }) optionLabel: ElementRef;
	/**
	 * Full url of endpoint without any GET params.
	 *
	 */
	@Input() endpointUrl;
	@Input() options: any[] = [];
	@Input() initOption: any;
	@Input() control: FormControl;
	@Input() paramBuilder: SelectFilterParamBuilder;
	@Input() resposeParser: SelectFilterResponseParser;
	@Input() placeholder = "-";
	@Input() label;
	@Input() idKey = "id";
	@Input() labelKey = "label";

	@Input()
	public sortKey: string;

	@Input() labelFn: (item: any) => string;
	@Input() subLabelFn: (item: any) => string;
	@Input() imageKey;
	@Input() hasClearAction = true;
	@Input() subLabel = true;
	@Input() size: PactoSelectSize = PactoSelectSize.NORMAL;
	@Input() addEmptyOption = false;
	@Input() addtionalFilters: any;
	@Output() onSearch: EventEmitter<string> = new EventEmitter<string>();

	@ViewChild("currentElRef", { static: true })
	currentElRef: ElementRef;
	@ViewChild("selectArea", { static: true }) selectArea: ElementRef;
	@ViewChild("filter", { static: true }) filter;

	@ContentChild(SelectItemDirective, { static: false })
	selectItem: SelectItemDirective;

	private searchSubscription: Subscription;

	filterFC: FormControl = new FormControl("");
	optionsOpen = false;
	loading = false;

	private fetchDataCache = new Map<string, any>();
	private lastFetchTime: number = 0;
	private readonly CACHE_DURATION: number = 30000; // 30s
	private readonly TIME_TO_ENABLE_RELOAD: number = 30000;

	canReload: boolean = false;
	private timeToEnableReloadInterval;

	constructor(
		private compRef: ElementRef,
		private http: HttpClient,
		private cd: ChangeDetectorRef,
		private util: PactoUtilService,
		private notificationService: SnotifyService
	) {}

	get small() {
		return this.size === PactoSelectSize.SMALL;
	}

	get isInvalid() {
		return this.control && this.control.touched && !this.control.valid;
	}

	get error() {
		return this.isInvalid && !this.control.disabled;
	}

	get currentOption() {
		if (this.control) {
			return this.control.value;
		} else {
			return null;
		}
	}

	get filteredOptions() {
		if (this.endpointUrl) {
			return this.options;
		} else {
			return this.options.filter((item) => {
				const token = item[this.labelKey];
				let filter = this.filterFC.value;
				filter = filter ? filter : "";
				const regex = new RegExp(`${filter}`, "gi");
				return regex.test(token);
			});
		}
	}

	getOptionLabel(option: any): string {
		if (this.labelFn) {
			return this.labelFn(option);
		} else {
			let label = option[this.labelKey];
			if (!label || label === "") {
				if (this.options && this.control.value != null) {
					const optionFinded = this.options.find(
						(opt) => opt[this.idKey] === this.control.value[this.idKey]
					);
					if (optionFinded && optionFinded !== -1) {
						label = optionFinded[this.labelKey];
					} else {
						label = this.placeholder;
					}
				} else {
					label = this.placeholder;
				}
			}
			return label;
		}
	}

	getOptionSubLabel(option: any): string {
		if (this.subLabelFn) {
			return this.subLabelFn(option);
		} else {
			let label = option[this.labelKey];
			if (!label || label === "") {
				if (this.options) {
					const optionFinded = this.options.find(
						(opt) => opt[this.idKey] === this.control.value[this.idKey]
					);
					if (optionFinded && optionFinded !== -1) {
						label = optionFinded[this.labelKey];
					} else {
						label = this.placeholder;
					}
				} else {
					label = this.placeholder;
				}
			}
			return label;
		}
	}

	ngOnInit() {
		if (!this.control) {
			this.control = new FormControl();
		}
		if (!this.options) {
			this.options = [];
		}
		this.validateId();
		this.control.registerOnDisabledChange((disabled) => {
			if (disabled) {
				this.optionsOpen = false;
			}
		});

		/**
		 * Cancel result callback
		 */
		this.filterFC.valueChanges.subscribe(() => {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
		});

		/**
		 * Trigger search after delay
		 */
		this.filterFC.valueChanges.pipe(debounceTime(500)).subscribe((term) => {
			this.search(term);
		});
		this.control.valueChanges.subscribe(() => {
			this.cd.detectChanges();
		});

		if (this.control.value) {
			this.search(null, this.control.value, true);
		}
		this._initReloadInterval();
	}

	ngAfterViewInit() {}

	ngOnDestroy() {
		this._clearReloadInterval();
		this.fetchDataCache.clear();
	}

	clearHandler(event: MouseEvent) {
		if (this.control.enabled) {
			event.stopPropagation();
			this.control.reset(null);
		}
	}

	currentClickHandler($event: MouseEvent) {
		if (this.isSelectAreaClick($event) && this.control.enabled) {
			if (!this.optionsOpen) {
				this.open();
			} else {
				this.optionsOpen = false;
			}
		}
	}

	selectOptionHandler(option, init = false) {
		this.control.setValue(option);
		if (!init) {
			this.control.markAsDirty();
		}
		this.optionsOpen = false;
	}

	@HostListener("document:click", ["$event"])
	clickDocumentHandler($event) {
		if (!this.isComponentClick($event)) {
			this.optionsOpen = false;
		}
	}

	private open() {
		this.filterFC.setValue("", { emitEvent: false });
		this.search(null, null, false);
		this.optionsOpen = true;
		setTimeout(() => {
			this.filter.nativeElement.focus();
		}, 200);
	}

	search(term, initValue = null, reload = true, refreshClick: boolean = false) {
		if (this.endpointUrl) {
			this.loading = true;
			this.cd.detectChanges();
			this.searchSubscription = this.fetchData(
				term,
				reload,
				refreshClick
			).subscribe((result) => {
				this.loading = false;
				this.options = result;

				if (this.initOption && this.options && Array.isArray(this.options)) {
					const existOption = this.options.some(
						(option) => option[this.idKey] === this.initOption[this.idKey]
					);
					if (!existOption) {
						if (!this.initOption[this.idKey]) {
							if (typeof initValue !== "object") {
								this.initOption[this.idKey] = initValue;
							}
						}
						this.options.push(this.initOption);
					}
				}

				if (this.addEmptyOption) {
					const emptyOption: any = {};
					emptyOption[this.idKey] = null;
					emptyOption[this.labelKey] = "-";
					this.options.unshift(emptyOption);
				}
				if (initValue) {
					let valueToSelect = initValue;
					if (initValue instanceof Object) {
						valueToSelect = initValue[this.idKey];
					}
					this.selectOptionHandler(
						this.options.find((option) => option[this.idKey] === valueToSelect),
						true
					);
				}
				this.cd.detectChanges();
			});
		} else {
			if (this.addEmptyOption) {
				const emptyOption: any = {};
				emptyOption[this.idKey] = null;
				emptyOption[this.labelKey] = "-";
				if (
					!this.options.find(
						(o) => o[this.labelKey] === emptyOption[this.labelKey]
					)
				) {
					this.options.unshift(emptyOption);
				}
			}
			this.onSearch.emit(term);
		}
	}

	private fetchData(
		term: string,
		reload = false,
		refreshClick: boolean = false
	): Observable<any> {
		const cacheKey = `${this.id}`;
		const now = Date.now();

		const currentDataCache = this.fetchDataCache.get(cacheKey);
		const lowercaseTerm = term ? term.toLowerCase() : term;

		if (
			reload &&
			refreshClick &&
			now - this.lastFetchTime < this.TIME_TO_ENABLE_RELOAD
		) {
			this.notificationService.warning(
				`Houve uma atualização nos últimos ${
					this.TIME_TO_ENABLE_RELOAD / 1000
				}s, aguarde um momento para atualizar novamente!`
			);
			this._resetReloadInterval(false);
			return of(currentDataCache || []);
		}

		if (
			currentDataCache &&
			now - this.lastFetchTime < this.CACHE_DURATION &&
			!reload
		) {
			if (this.searchSubscription) {
				this.searchSubscription.unsubscribe();
			}
			let filtered = currentDataCache.filter((x) => {
				if (
					x[this.labelKey].toLowerCase().indexOf(lowercaseTerm) !== -1 ||
					!lowercaseTerm ||
					lowercaseTerm.length === 0
				) {
					return x;
				}
			});
			if (filtered.length !== 0) {
				filtered = this._sortValues(filtered);
				return of(filtered);
			}
		}

		const url = this.endpointUrl;
		const params = this.paramBuilder ? this.paramBuilder(term) : {};

		if (this.addtionalFilters) {
			const filters = JSON.parse(params.filters.toString());
			Object.keys(this.addtionalFilters).forEach((key) => {
				filters[key] = this.addtionalFilters[key];
			});
			params.filters = JSON.stringify(filters);
		}

		const data = this.http.get(url, { params });

		return data.pipe(
			map((result) => {
				this._resetReloadInterval(false);
				const parsedResult: any = this.resposeParser
					? this.resposeParser(result)
					: result;
				const resultContent = parsedResult.content
					? parsedResult.content
					: parsedResult;
				// Cache the result
				if (this.fetchDataCache.has(cacheKey)) {
					let actualResult = this.fetchDataCache.get(cacheKey);
					const actualResultContent = actualResult.map((v) => v[this.labelKey]);

					resultContent.forEach((rc) => {
						if (!actualResultContent.includes(rc[this.labelKey])) {
							actualResult.push(rc);
						}
					});

					actualResult = this._sortValues(actualResult);

					this.fetchDataCache.set(cacheKey, actualResult);
				} else {
					this.fetchDataCache.set(cacheKey, resultContent);
				}
				this.lastFetchTime = now;
				return resultContent;
			}),
			catchError(() => {
				if (this.resposeParser) {
					return of(this.resposeParser([]));
				} else {
					return of([]);
				}
			})
		);
	}

	private validateId() {
		if (!this.id) {
			this.id = "cat-select-filter-id-" + Math.trunc(Math.random() * 1000);
		}
	}

	private isSelectAreaClick($event: MouseEvent) {
		return this.isComponentClick($event) && !this.isOptionAreaClick($event);
	}

	private isComponentClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.compRef.nativeElement,
			$event.target as Element
		);
	}

	private isOptionAreaClick($event: MouseEvent) {
		return this.util.isDescendant(
			this.selectArea.nativeElement,
			$event.target as Element
		);
	}

	private _resetReloadInterval(canReload) {
		this.canReload = canReload;
		this._clearReloadInterval();
		this._initReloadInterval();
	}

	private _initReloadInterval() {
		if (!this.endpointUrl) {
			return;
		}
		this.timeToEnableReloadInterval = setInterval(() => {
			if (this.canReload) {
				this._clearReloadInterval();
				return;
			}
			this.canReload = true;
			this.cd.markForCheck();
		}, this.TIME_TO_ENABLE_RELOAD);
	}

	private _clearReloadInterval() {
		clearInterval(this.timeToEnableReloadInterval);
		this.timeToEnableReloadInterval = null;
	}

	private _sortValues(data) {
		const sortKey = this.sortKey ? this.sortKey : this.labelKey;
		return data.sort((a, b) => {
			const aValue = a[sortKey] ? a[sortKey] : "";
			const bValue = b[sortKey] ? b[sortKey] : "";
			if (aValue > bValue) {
				return 1;
			} else if (aValue < bValue) {
				return -1;
			} else {
				return 0;
			}
		});
	}
}
