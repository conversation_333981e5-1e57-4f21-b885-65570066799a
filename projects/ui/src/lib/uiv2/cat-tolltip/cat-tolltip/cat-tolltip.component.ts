import {
	AfterViewInit,
	Component,
	ElementRef,
	Inject,
	Input,
	OnDestroy,
	OnInit,
	Renderer2,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { DOCUMENT } from "@angular/common";
import { TooltipManagerService } from "../services/tooltip-manager.service";

let uniqueId = 0;

@Component({
	selector: "[pactoCatTolltip]",
	templateUrl: "./cat-tolltip.component.html",
	styleUrls: ["./cat-tolltip.component.scss"],
})
export class CatTolltipComponent implements OnInit, AfterViewInit, OnDestroy {
	@Input() darkTheme: boolean = false;
	@Input() pactoCatTolltip: TemplateRef<any> | string;

	@Input() position: "left" | "right" | "top" | "bottom" = "top";
	@ViewChild("tooltip", { static: false }) tooltip: ElementRef;

	overlay: HTMLElement;

	render2Listeners: Array<() => any> = new Array<() => any>();

	constructor(
		private render2: Renderer2,
		private elementRef: ElementRef,
		@Inject(DOCUMENT) private document,
		private tooltipManager: TooltipManagerService
	) {}

	ngOnInit() {
		this.render2.setStyle(
			this.elementRef.nativeElement,
			"position",
			"relative"
		);
		this.createOverlay();

		// Registra este tooltip no gerenciador global
		this.tooltipManager.registerTooltip(this);
	}

	ngOnDestroy() {
		this.render2Listeners.forEach((i) => {
			i();
		});

		// Remove este tooltip do gerenciador global
		this.tooltipManager.unregisterTooltip(this);
	}

	ngAfterViewInit() {
		if (!this.elementRef.nativeElement.id) {
			this.render2.setAttribute(
				this.elementRef.nativeElement,
				"id",
				`cat-tooltip-trigger-${uniqueId++}`
			);
		}
		if (!this.tooltip.nativeElement.id) {
			this.render2.setAttribute(
				this.tooltip.nativeElement,
				"id",
				`cat-tooltip-${this.elementRef.nativeElement.id}`
			);
		}
		const listenerEnter = this.render2.listen(
			this.elementRef.nativeElement,
			"mouseenter",
			() => {
				this.overlay.appendChild(this.tooltip.nativeElement);
				this.tooltip.nativeElement.style.visibility = "visible";
				this.calculateTooltipPosition();
			}
		);
		const listenerLeave = this.render2.listen(
			this.elementRef.nativeElement,
			"mouseleave",
			() => {
				this.tooltip.nativeElement.style.visibility = "hidden";
				this.removeChildTolltip();
			}
		);

		this.render2Listeners.push(listenerEnter, listenerLeave);

		// TODO Resolver bug durante scroll.
		// this.render2.listen(this.overlay, 'scroll', (event) => {
		//     console.log(event);
		//     this.calculateTooltipPosition();
		// });
	}

	/**
	 * Método chamado pelo TooltipManagerService quando há um clique global
	 * @param event Evento de clique
	 */
	public handleDocumentClick(event: Event): void {
		if (this.tooltip && this.isVisible()) {
			const target = event.target as Element;
			const isInside = this.tooltip.nativeElement.contains(target);

			if (!isInside) {
				this.tooltip.nativeElement.style.visibility = "hidden";
				this.removeChildTolltip();
			}
		}
	}

	/**
	 * Verifica se o tooltip está visível
	 */
	public isVisible(): boolean {
		return (
			this.tooltip &&
			this.tooltip.nativeElement &&
			this.tooltip.nativeElement.style.visibility === "visible"
		);
	}

	/**
	 * Força o tooltip a ficar oculto (usado pelo service)
	 */
	public forceHide(): void {
		if (this.tooltip && this.tooltip.nativeElement) {
			this.tooltip.nativeElement.style.visibility = "hidden";
			this.removeChildTolltip();
		}
	}

	private removeChildTolltip() {
		if (this.overlay) {
			if (this.overlay.children.length === 0) {
				return;
			}
			const children = Array.from(this.overlay.children);
			for (const child of children) {
				if (child.id.includes(this.tooltip.nativeElement.id)) {
					this.overlay.removeChild(child);
					break;
				}
			}
		}
	}

	private calculateTooltipPosition() {
		const triggerRect: ClientRect | DOMRect =
			this.elementRef.nativeElement.getBoundingClientRect();
		const tooltipRect: ClientRect | DOMRect =
			this.tooltip.nativeElement.getBoundingClientRect();
		if (this.position === "top") {
			this.tooltip.nativeElement.style.top =
				triggerRect.top - tooltipRect.height - 10 + "px";
			this.tooltip.nativeElement.style.left =
				triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2 + "px";
		} else if (this.position === "bottom") {
			this.tooltip.nativeElement.style.top =
				triggerRect.top + triggerRect.height + 10 + "px";
			this.tooltip.nativeElement.style.left =
				triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2 + "px";
		} else if (this.position === "left") {
			this.tooltip.nativeElement.style.left =
				triggerRect.left - tooltipRect.width - 10 + "px";
			this.tooltip.nativeElement.style.top =
				triggerRect.top +
				triggerRect.height / 2 -
				tooltipRect.height / 2 +
				"px";
		} else if (this.position === "right") {
			this.tooltip.nativeElement.style.left = triggerRect.right + 10 + "px";
			this.tooltip.nativeElement.style.top =
				triggerRect.top +
				triggerRect.height / 2 -
				tooltipRect.height / 2 +
				"px";
		}
	}

	createOverlay() {
		this.overlay = this.document.getElementById("pacto-cat-overlay");
		if (!this.overlay) {
			this.overlay = this.document.createElement("div");
			this.overlay.id = "pacto-cat-overlay";
			this.overlay.style.position = "absolute";
			this.overlay.style.top = "0px";
			this.overlay.style.bottom = "0px";
			this.overlay.style.width = "100%";
			this.overlay.style.pointerEvents = "none";
			this.overlay = document.body.appendChild(this.overlay);
		}
	}

	public isTemplate(): boolean {
		return (
			typeof this.pactoCatTolltip !== "string" &&
			typeof this.pactoCatTolltip !== undefined
		);
	}
}
