import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TableData,
	TraducoesXinglingComponent,
} from "ui-kit";
import { FormControl, FormGroup } from "@angular/forms";
import { DecimalPipe } from "@angular/common";
import { SnotifyService } from "ng-snotify";
import { AdmCoreApiIntegracoesService } from "adm-core-api";

@Component({
	selector: "adm-table-foguete-duracao-config",
	templateUrl: "./table-foguete-duracao-config.component.html",
	styleUrls: ["./table-foguete-duracao-config.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableFogueteDuracaoConfigComponent
	implements OnInit, AfterViewInit
{
	tableConvenioDescontoConfiguracao: PactoDataGridConfig;
	planosDuracoes: any[] = [];

	@Input() idIntegracaoFoguete: number;
	@Output() afterEditAdd: EventEmitter<any> = new EventEmitter<any>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("columnDuracao", { static: true }) columnDuracao: TemplateRef<any>;

	@ViewChild("tableConvenioDescontoComponent", { static: true })
	tableConvenioDescontoComponent: CatTableEditableComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	constructor(
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.cd.detectChanges();
		this.admCoreApiIntegracoes
			.consultarConfiguracaoIntegracaoFogueteDuracaoPlano(
				this.idIntegracaoFoguete
			)
			.subscribe((response) => {
				const tipos = [];
				response.content.forEach((item, index) => {
					tipos.push({ duracao: item });
				});

				this.planosDuracoes = tipos;
				this.tableConvenioDescontoComponent.reloadData();
			});
	}

	ngAfterViewInit() {
		this.initTableConvenioDescontoConfiguracao();
		this.cd.detectChanges();
	}

	initTableConvenioDescontoConfiguracao() {
		this.tableConvenioDescontoConfiguracao = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.planosDuracoes,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				duracao: new FormControl(),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "duracao",
					titulo: this.columnDuracao,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					minValue: 0,
					errorMessage: this.traducoes.getLabel("error-valor-obrigatorio"),
					width: "200px",
				},
			],
		});
		this.cd.detectChanges();
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		if (form.get("duracao").value <= 0) {
			this.notificationService.error(
				this.traducoes.getLabel("error-duracao-invalida-table")
			);
			return false;
		}

		const existsDuracao = data.find(
			(value, index) =>
				form.get("duracao").value === value.duracao && index !== rowIndex
		);
		if (existsDuracao) {
			this.notificationService.error(
				this.traducoes.getLabel("error-duracao-duplicado-table")
			);
			return false;
		}
		return true;
	}

	confirm(event) {
		const duracoesPlano = [];
		event.data.forEach(i => duracoesPlano.push(i.duracao));
		this.afterEditAdd.emit({ duracoesPlano });
	}

	delete(event) {
		if (this.planosDuracoes) {
			const duracoesPlano = [];
			this.planosDuracoes.splice(event.index, 1);
			this.planosDuracoes.forEach(i => duracoesPlano.push(i.duracao));
			this.afterEditAdd.emit( { duracoesPlano });
		}
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
	}
}
