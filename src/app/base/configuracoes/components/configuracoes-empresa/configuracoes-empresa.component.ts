import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";

import { TreinoApiConfiguracoesTreinoService } from "treino-api";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import { SessionService } from "@base-core/client/session.service";
import { SeletorImagemComponent } from "old-ui-kit";
import { PerfilAcessoRecursoNome } from "treino-api";
import { EmpresaImageSyncService } from "../../services/empresa-image-sync.service";

@Component({
	selector: "pacto-configuracoes-empresa",
	templateUrl: "./configuracoes-empresa.component.html",
	styleUrls: ["./configuracoes-empresa.component.scss"],
})
export class ConfiguracoesEmpresaComponent implements OnInit {
	@ViewChild("configSuccess", { static: true }) configSuccess;
	@ViewChild("configError", { static: true }) configError;
	@ViewChild("seletorLogoEmpresa", { static: true })
	seletorLogoEmpresa: SeletorImagemComponent;
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		nome: new FormControl(null),
		timeZoneDefault: new FormControl(null),
		keyImgEmpresa: new FormControl(null),
	});

	ready = false;
	saving = false;
	items: any[] = [];
	integradoZw = false;

	timeZone: any[] = [
		{ id: "GMT-2", nome: "GMT-2" },
		{ id: "GMT-3", nome: "GMT-3" },
		{ id: "GMT-4", nome: "GMT-4" },
		{ id: "GMT-5", nome: "GMT-5" },
	];

	constructor(
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private configCache: TreinoConfigCacheService,
		private notify: SnotifyService,
		private session: SessionService,
		private empresaImageSync: EmpresaImageSyncService
	) {}

	clearedImageHandler(image) {
		this.formGroup.get(image).setValue(null);
	}

	ngOnInit() {
		if (Object.keys(this.configCache.configuracoesEmpresa).length === 0) {
			this.configCache.loadTreinoConfigCache().subscribe(() => {
				this.inicializarComponente();
			});
		} else {
			this.inicializarComponente();
		}
	}

	private inicializarComponente() {
		this.integradoZw = this.session.integracaoZW;
		this.setupFormGroup();
		this.formGroup.setValue(this.configCache.configuracoesEmpresa);
		this.configurarImagemInicial();

		this.verificarPermissoes();
	}

	private configurarImagemInicial() {
		const keyImgEmpresa = this.configCache.configuracoesEmpresa.keyImgEmpresa;
		this.empresaImageSync.updateEmpresaImage(keyImgEmpresa);
		const imageUrl = this.empresaImageSync.convertToDataUrl(keyImgEmpresa);
		this.seletorLogoEmpresa.setUrl(imageUrl);
	}

	private setupFormGroup() {
		this.items.forEach((item) => {
			this.formGroup.addControl(item.name, new FormControl(null));
			item.formControl = this.formGroup.get(item.name) as FormControl;
		});
		this.ready = true;
	}

	saveHandler() {
		const dto = this.formGroup.getRawValue();

		if (this.validarTamanhoArquivo()) {
			this.saveConfig();
		}
	}

	private validarTamanhoArquivo(): boolean {
		const inputElement = this.seletorLogoEmpresa.inputImagem.nativeElement;
		const files = inputElement.files;
		const maxSizeInBytes = 2097152; // 2MB em bytes

		if (files && files.length > 0) {
			if (files[0].size > maxSizeInBytes) {
				const configError = this.configError.nativeElement.innerHTML;
				this.notify.error(configError);
				return false;
			}
		}

		return true;
	}

	private saveConfig() {
		const dto = this.formGroup.getRawValue();
		const save$ = this.configTreinoService.updateConfiguracoesEmpresa(dto);
		const update$ = this.configCache.loadTreinoConfigCache();
		return save$.pipe(switchMap(() => update$)).subscribe(() => {
			this.sincronizarImagemComSession(dto.keyImgEmpresa);
			this.atualizarImagemSeletor(dto.keyImgEmpresa);
			const configSuccess = this.configSuccess.nativeElement.innerHTML;
			this.notify.success(configSuccess);
		});
	}

	private sincronizarImagemComSession(keyImgEmpresa: string) {
		this.empresaImageSync.updateEmpresaImage(keyImgEmpresa);
	}

	private atualizarImagemSeletor(keyImgEmpresa: string) {
		const imageUrl = this.empresaImageSync.convertToDataUrl(keyImgEmpresa);
		this.seletorLogoEmpresa.setUrl(imageUrl);
	}

	verificarPermissoes() {
		const configuracoesEmpresa = this.session.recursos.get(
			PerfilAcessoRecursoNome.CONFIGURACOES_EMPRESA
		);
		if (
			configuracoesEmpresa &&
			!configuracoesEmpresa.incluir &&
			!configuracoesEmpresa.editar &&
			!configuracoesEmpresa.excluir
		) {
			this.formGroup.disable();
			this.saving = true;
		}
	}
}
